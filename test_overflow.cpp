#include "lib/rrjson.hpp"
#include <iostream>
#include <limits>

int main() {
    try {
        // Test normal int conversion
        std::string json1 = R"({"value": 42})";
        rrjson::Element root1(std::move(json1));
        std::cout << "Normal int: " << root1["value"].as_int() << std::endl;

        // Test large number that fits in int
        std::string json2 = R"({"value": 2147483647})";  // INT_MAX
        rrjson::Element root2(std::move(json2));
        std::cout << "INT_MAX: " << root2["value"].as_int() << std::endl;

        // Test number that's too large for int
        std::string json3 = R"({"value": 9999999999999999999})";
        rrjson::Element root3(std::move(json3));
        try {
            auto large_int = root3["value"].as_int();
            std::cout << "ERROR: Should have thrown overflow_error!" << std::endl;
            std::cout << "Got: " << large_int << std::endl;
        } catch (const std::overflow_error& e) {
            std::cout << "Correctly caught overflow_error: " << e.what() << std::endl;
        }

        // Test negative number
        std::string json4 = R"({"value": -42})";
        rrjson::Element root4(std::move(json4));
        std::cout << "Negative int: " << root4["value"].as_int() << std::endl;

        std::cout << "Overflow protection test passed!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
