-- Project configuration
set_project("rrjson")
set_version("1.0.0")
set_languages("c++23")

-- Build modes
add_rules("mode.debug", "mode.release")

-- Set compiler flags
set_warnings("all", "error")
if is_mode("debug") then
    set_symbols("debug")
    set_optimize("none")
elseif is_mode("release") then
    set_symbols("hidden")
    set_optimize("fastest")
    set_strip("all")
end

-- Header-only library target
target("rrjson")
    set_kind("headeronly")
    add_headerfiles("lib/**.hpp")
    add_includedirs("lib", {public = true})

-- Example target
target("example")
    set_kind("binary")
    add_deps("rrjson")
    add_files("examples/basic_usage.cpp")
    add_includedirs("lib")

-- Comprehensive test target
target("comprehensive")
    set_kind("binary")
    add_deps("rrjson")
    add_files("examples/comprehensive_test.cpp")
    add_includedirs("lib")

-- Performance test target
target("performance")
    set_kind("binary")
    add_deps("rrjson")
    add_files("examples/performance_test.cpp")
    add_includedirs("lib")

-- Benchmark comparison target
target("benchmark")
    set_kind("binary")
    add_deps("rrjson")
    add_files("benchmarks/json_benchmark.cpp")
    add_includedirs("lib")

    -- Try to add packages if available
    on_config(function (target)
        import("lib.detect.find_package")

        -- Check for nlohmann/json
        local nlohmann = find_package("nlohmann_json")
        if nlohmann then
            target:add("packages", "nlohmann_json")
            target:add("defines", "HAS_NLOHMANN_JSON")
        end

        -- Check for rapidjson
        local rapidjson = find_package("rapidjson")
        if rapidjson then
            target:add("packages", "rapidjson")
            target:add("defines", "HAS_RAPIDJSON")
        end

        -- Check for simdjson
        local simdjson = find_package("simdjson")
        if simdjson then
            target:add("packages", "simdjson")
            target:add("defines", "HAS_SIMDJSON")
        end

        -- Check for boost
        local boost = find_package("boost")
        if boost then
            target:add("packages", "boost")
            target:add("defines", "HAS_BOOST_JSON")
        end
    end)

    set_default(false)

-- Test target
target("test")
    set_kind("binary")
    add_deps("rrjson")
    add_files("tests/*.cpp")
    add_includedirs("lib")
    set_default(false)

-- Installation
target("rrjson")
    on_install(function (target)
        local headerdir = path.join(target:installdir(), "include", "rrjson")
        os.cp("lib/*.hpp", headerdir)
    end)