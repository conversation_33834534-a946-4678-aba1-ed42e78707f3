#include "lib/rrjson.hpp"
#include <iostream>
#include <limits>

int main() {
    try {
        // Test normal int conversion
        std::string json1 = R"({"value": 42})";
        rrjson::Element root1(std::move(json1));
        std::cout << "Normal int: " << root1["value"].as_int() << std::endl;

        // Test large number that should trigger overflow
        std::string json2 = R"({"value": 1e19})";  // Scientific notation for large number
        rrjson::Element root2(std::move(json2));
        try {
            [[maybe_unused]] auto large_int = root2["value"].as_int();
            std::cout << "ERROR: Should have thrown overflow_error!" << std::endl;
        } catch (const std::overflow_error& e) {
            std::cout << "✓ Correctly caught overflow_error: " << e.what() << std::endl;
        }

        // Test negative number that should trigger underflow
        std::string json3 = R"({"value": -1e19})";
        rrjson::Element root3(std::move(json3));
        try {
            [[maybe_unused]] auto underflow_int = root3["value"].as_int();
            std::cout << "ERROR: Should have thrown overflow_error!" << std::endl;
        } catch (const std::overflow_error& e) {
            std::cout << "✓ Correctly caught underflow overflow_error: " << e.what() << std::endl;
        }

        // Test edge case: exactly INT_MAX
        std::string json4 = R"({"value": 2147483647})";  // INT_MAX
        rrjson::Element root4(std::move(json4));
        std::cout << "INT_MAX: " << root4["value"].as_int() << std::endl;

        // Test edge case: exactly INT_MIN
        std::string json5 = R"({"value": -2147483648})";  // INT_MIN
        rrjson::Element root5(std::move(json5));
        std::cout << "INT_MIN: " << root5["value"].as_int() << std::endl;

        std::cout << "✓ All overflow protection tests passed!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
