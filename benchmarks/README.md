# JSON Library Benchmark

This benchmark compares rrjson performance against other popular C++ JSON libraries:

- **nlohmann/json** - Most popular, convenient API
- **RapidJSON** - High performance, widely used  
- **simdjson** - Fastest parsing (read-only)
- **Boost.JSON** - Good balance of performance and convenience

## Building and Running

### Prerequisites

Install the required JSON libraries. On Ubuntu/Debian:

```bash
# Install development packages
sudo apt update
sudo apt install -y build-essential cmake pkg-config

# Install Boost
sudo apt install -y libboost-all-dev

# Install nlohmann/json
sudo apt install -y nlohmann-json3-dev

# Install RapidJSON
sudo apt install -y rapidjson-dev

# Install simdjson (may need to build from source)
git clone https://github.com/simdjson/simdjson.git
cd simdjson
mkdir build && cd build
cmake ..
make -j$(nproc)
sudo make install
```

### Using xmake (Recommended)

```bash
# Install xmake if not already installed
curl -fsSL https://xmake.io/shget.text | bash

# Install dependencies
xmake require nlohmann_json rapidjson simdjson boost

# Build benchmark
xmake build benchmark

# Run benchmark
xmake run benchmark
```

### Manual Build

```bash
# Create build directory
mkdir -p build && cd build

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
make -j$(nproc)

# Run
./json_benchmark
```

## Benchmark Tests

The benchmark tests three different dataset sizes:

1. **Small** - 100 JSON objects (~15KB)
2. **Medium** - 1000 JSON objects (~150KB) 
3. **Large** - 10000 JSON objects (~1.5MB)

For each dataset, the benchmark measures:

- **Parse Time** - Time to parse JSON from string
- **Access Time** - Time to access nested elements
- **Error Handling** - Time to handle various error conditions
- **Memory Usage** - Estimated memory consumption

## Expected Results

Based on typical performance characteristics:

- **simdjson** - Fastest parsing, lowest memory usage
- **RapidJSON** - Very fast, good memory efficiency
- **rrjson** - Competitive performance with modern C++ features
- **Boost.JSON** - Good balance of speed and convenience
- **nlohmann/json** - Slower but most convenient API

## Interpreting Results

- Lower times are better for all metrics
- Memory usage is estimated and may not be perfectly accurate
- Results can vary significantly based on:
  - CPU architecture and features (SIMD support)
  - Compiler optimizations
  - JSON structure and content
  - System memory and cache performance

## Notes

- simdjson requires modern CPU with SIMD support
- Some libraries may not be available on all systems
- The benchmark will skip libraries that aren't found
- Compile with `-O3` or `-DCMAKE_BUILD_TYPE=Release` for accurate results
