#!/bin/bash

# JSON Library Dependencies Installation Script
# This script attempts to install JSON libraries for benchmarking

set -e

echo "JSON Library Dependencies Installation"
echo "====================================="
echo

# Detect the package manager
if command -v pacman &> /dev/null; then
    PKG_MANAGER="pacman"
    INSTALL_CMD="sudo pacman -S --needed"
    SEARCH_CMD="pacman -Ss"
elif command -v apt &> /dev/null; then
    PKG_MANAGER="apt"
    INSTALL_CMD="sudo apt install -y"
    SEARCH_CMD="apt search"
elif command -v dnf &> /dev/null; then
    PKG_MANAGER="dnf"
    INSTALL_CMD="sudo dnf install -y"
    SEARCH_CMD="dnf search"
elif command -v yum &> /dev/null; then
    PKG_MANAGER="yum"
    INSTALL_CMD="sudo yum install -y"
    SEARCH_CMD="yum search"
elif command -v zypper &> /dev/null; then
    PKG_MANAGER="zypper"
    INSTALL_CMD="sudo zypper install -y"
    SEARCH_CMD="zypper search"
else
    echo "Error: No supported package manager found (pacman, apt, dnf, yum, zypper)"
    echo "Please install the JSON libraries manually:"
    echo "  - nlohmann/json"
    echo "  - RapidJSON"
    echo "  - simdjson"
    echo "  - Boost (with JSON support)"
    exit 1
fi

echo "Detected package manager: $PKG_MANAGER"
echo

# Function to install packages based on the package manager
install_packages() {
    case $PKG_MANAGER in
        "pacman")
            echo "Installing packages for Arch Linux..."
            $INSTALL_CMD nlohmann-json rapidjson simdjson boost
            ;;
        "apt")
            echo "Installing packages for Ubuntu/Debian..."
            sudo apt update
            $INSTALL_CMD nlohmann-json3-dev rapidjson-dev libboost-all-dev
            
            # simdjson might need to be built from source on older systems
            if ! apt search simdjson | grep -q simdjson; then
                echo "simdjson not available in repositories, building from source..."
                install_simdjson_from_source
            else
                $INSTALL_CMD libsimdjson-dev
            fi
            ;;
        "dnf")
            echo "Installing packages for Fedora..."
            $INSTALL_CMD nlohmann-json-devel RapidJSON-devel boost-devel
            
            # Check if simdjson is available
            if dnf search simdjson | grep -q simdjson; then
                $INSTALL_CMD simdjson-devel
            else
                echo "simdjson not available in repositories, building from source..."
                install_simdjson_from_source
            fi
            ;;
        "yum")
            echo "Installing packages for CentOS/RHEL..."
            # Enable EPEL repository for additional packages
            $INSTALL_CMD epel-release
            $INSTALL_CMD boost-devel
            
            echo "Note: nlohmann/json and RapidJSON might need to be installed manually"
            echo "simdjson will be built from source..."
            install_simdjson_from_source
            ;;
        "zypper")
            echo "Installing packages for openSUSE..."
            $INSTALL_CMD nlohmann_json-devel rapidjson-devel boost-devel
            
            # Check if simdjson is available
            if zypper search simdjson | grep -q simdjson; then
                $INSTALL_CMD simdjson-devel
            else
                echo "simdjson not available in repositories, building from source..."
                install_simdjson_from_source
            fi
            ;;
    esac
}

# Function to build simdjson from source
install_simdjson_from_source() {
    echo "Building simdjson from source..."
    
    # Check if git and cmake are available
    if ! command -v git &> /dev/null; then
        echo "Error: git is required to build simdjson from source"
        return 1
    fi
    
    if ! command -v cmake &> /dev/null; then
        echo "Error: cmake is required to build simdjson from source"
        return 1
    fi
    
    # Create temporary directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Clone and build simdjson
    git clone https://github.com/simdjson/simdjson.git
    cd simdjson
    mkdir build && cd build
    cmake .. -DCMAKE_BUILD_TYPE=Release
    make -j$(nproc)
    sudo make install
    
    # Clean up
    cd /
    rm -rf "$TEMP_DIR"
    
    echo "simdjson built and installed successfully"
}

# Function to verify installations
verify_installations() {
    echo
    echo "Verifying installations..."
    echo "========================="
    
    # Check for nlohmann/json
    if find /usr/include -name "nlohmann" -type d 2>/dev/null | grep -q nlohmann; then
        echo "✓ nlohmann/json found"
    else
        echo "✗ nlohmann/json not found"
    fi
    
    # Check for RapidJSON
    if find /usr/include -name "rapidjson" -type d 2>/dev/null | grep -q rapidjson; then
        echo "✓ RapidJSON found"
    else
        echo "✗ RapidJSON not found"
    fi
    
    # Check for simdjson
    if find /usr/include -name "simdjson.h" 2>/dev/null | grep -q simdjson; then
        echo "✓ simdjson found"
    else
        echo "✗ simdjson not found"
    fi
    
    # Check for Boost
    if find /usr/include -path "*/boost/version.hpp" 2>/dev/null | grep -q boost; then
        echo "✓ Boost found"
        # Check specifically for Boost.JSON (available in Boost 1.75+)
        if find /usr/include -path "*/boost/json.hpp" 2>/dev/null | grep -q json; then
            echo "✓ Boost.JSON found"
        else
            echo "! Boost found but Boost.JSON may not be available (requires Boost 1.75+)"
        fi
    else
        echo "✗ Boost not found"
    fi
}

# Main installation process
echo "This script will install JSON libraries for benchmarking."
echo "The following libraries will be installed:"
echo "  - nlohmann/json"
echo "  - RapidJSON"
echo "  - simdjson"
echo "  - Boost (with JSON support if available)"
echo

read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Installation cancelled."
    exit 0
fi

# Install packages
install_packages

# Verify installations
verify_installations

echo
echo "Installation completed!"
echo
echo "You can now run the benchmark with all available libraries:"
echo "  cd benchmarks"
echo "  ./build_and_run.sh"
echo
echo "Or build manually with CMake:"
echo "  cd benchmarks"
echo "  mkdir build && cd build"
echo "  cmake .. -DCMAKE_BUILD_TYPE=Release"
echo "  make -j\$(nproc)"
echo "  ./json_benchmark"
