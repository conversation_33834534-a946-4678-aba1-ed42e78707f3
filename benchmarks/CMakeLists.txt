cmake_minimum_required(VERSION 3.16)
project(json_benchmark)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Enable optimizations for release builds
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -march=native")

# Find packages
find_package(PkgConfig QUIET)

# Try to find nlohmann/json
find_package(nlohmann_json QUIET)
if(nlohmann_json_FOUND)
    message(STATUS "Found nlohmann/json")
    add_compile_definitions(HAS_NLOHMANN_JSON)
    set(NLOHMANN_LIBRARIES nlohmann_json::nlohmann_json)
else()
    message(WARNING "nlohmann/json not found - will be skipped in benchmark")
    set(NLOHMANN_LIBRARIES "")
endif()

# Try to find RapidJSON
find_path(RAPIDJSON_INCLUDE_DIR rapidjson/rapidjson.h
    PATHS /usr/include /usr/local/include /opt/homebrew/include)
if(RAPIDJSON_INCLUDE_DIR)
    message(STATUS "Found RapidJSON at ${RAPIDJSON_INCLUDE_DIR}")
    add_compile_definitions(HAS_RAPIDJSON)
    set(RAPIDJSON_LIBRARIES "")
else()
    message(WARNING "RapidJSON not found - will be skipped in benchmark")
    set(RAPIDJSON_LIBRARIES "")
endif()

# Try to find simdjson
find_package(simdjson QUIET)
if(simdjson_FOUND)
    message(STATUS "Found simdjson")
    add_compile_definitions(HAS_SIMDJSON)
    set(SIMDJSON_LIBRARIES simdjson::simdjson)
else()
    # Try pkg-config
    if(PkgConfig_FOUND)
        pkg_check_modules(SIMDJSON QUIET simdjson)
        if(SIMDJSON_FOUND)
            message(STATUS "Found simdjson via pkg-config")
            add_compile_definitions(HAS_SIMDJSON)
            set(SIMDJSON_LIBRARIES ${SIMDJSON_LIBRARIES})
        else()
            message(WARNING "simdjson not found - will be skipped in benchmark")
            set(SIMDJSON_LIBRARIES "")
        endif()
    else()
        message(WARNING "simdjson not found - will be skipped in benchmark")
        set(SIMDJSON_LIBRARIES "")
    endif()
endif()

# Try to find Boost.JSON
find_package(Boost QUIET COMPONENTS json)
if(Boost_FOUND)
    message(STATUS "Found Boost.JSON")
    add_compile_definitions(HAS_BOOST_JSON)
    set(BOOST_LIBRARIES Boost::json)
else()
    # Try to find Boost without specific components (older versions)
    find_package(Boost QUIET)
    if(Boost_FOUND)
        # Check if we can find boost/json.hpp
        find_path(BOOST_JSON_INCLUDE_DIR boost/json.hpp
            PATHS ${Boost_INCLUDE_DIRS} /usr/include /usr/local/include)
        if(BOOST_JSON_INCLUDE_DIR)
            message(STATUS "Found Boost.JSON (header-only)")
            add_compile_definitions(HAS_BOOST_JSON)
            set(BOOST_LIBRARIES ${Boost_LIBRARIES})
        else()
            message(WARNING "Boost.JSON not found - will be skipped in benchmark")
            set(BOOST_LIBRARIES "")
        endif()
    else()
        message(WARNING "Boost not found - Boost.JSON will be skipped in benchmark")
        set(BOOST_LIBRARIES "")
    endif()
endif()

# Create the benchmark executable
add_executable(json_benchmark json_benchmark.cpp)

# Include directories
target_include_directories(json_benchmark PRIVATE 
    ../lib
    ${RAPIDJSON_INCLUDE_DIR}
    ${SIMDJSON_INCLUDE_DIRS}
    ${Boost_INCLUDE_DIRS}
)

# Link libraries
target_link_libraries(json_benchmark PRIVATE
    ${NLOHMANN_LIBRARIES}
    ${RAPIDJSON_LIBRARIES}
    ${SIMDJSON_LIBRARIES}
    ${BOOST_LIBRARIES}
)

# Compiler-specific flags
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    target_compile_options(json_benchmark PRIVATE -Wall -Wextra -Wpedantic)
endif()

# Print summary
message(STATUS "")
message(STATUS "JSON Benchmark Configuration Summary:")
message(STATUS "=====================================")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "")
message(STATUS "Libraries that will be benchmarked:")
message(STATUS "- rrjson: YES (always included)")
if(nlohmann_json_FOUND)
    message(STATUS "- nlohmann/json: YES")
else()
    message(STATUS "- nlohmann/json: NO")
endif()
if(RAPIDJSON_INCLUDE_DIR)
    message(STATUS "- RapidJSON: YES")
else()
    message(STATUS "- RapidJSON: NO")
endif()
if(simdjson_FOUND OR SIMDJSON_FOUND)
    message(STATUS "- simdjson: YES")
else()
    message(STATUS "- simdjson: NO")
endif()
if(Boost_FOUND AND (TARGET Boost::json OR BOOST_JSON_INCLUDE_DIR))
    message(STATUS "- Boost.JSON: YES")
else()
    message(STATUS "- Boost.JSON: NO")
endif()
message(STATUS "")
