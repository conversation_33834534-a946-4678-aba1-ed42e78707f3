# JSON Library Benchmark Results

## Test Environment
- **System**: Arch Linux
- **Compiler**: GCC with C++23 support
- **Optimization**: -O3 -march=native
- **Available Libraries**: rrjson, simdjson

## Benchmark Results

### Small Dataset (100 objects, ~24KB)
| Library  | Parse (ms) | Access (ms) | Errors (ms) | Memory (KB) | Status |
|----------|------------|-------------|-------------|-------------|--------|
| rrjson   | 0.644      | 0.256       | 0.232       | 24          | ✓      |
| simdjson | 0.102      | 0.000       | 0.000       | 23          | ✓      |

### Medium Dataset (1000 objects, ~241KB)
| Library  | Parse (ms) | Access (ms) | Errors (ms) | Memory (KB) | Status |
|----------|------------|-------------|-------------|-------------|--------|
| rrjson   | 6.949      | 1.790       | 2.120       | 236         | ✓      |
| simdjson | 0.301      | 0.000       | 0.000       | 235         | ✓      |

### Large Dataset (10000 objects, ~2.4MB)
| Library  | Parse (ms) | Access (ms) | Errors (ms) | Memory (KB) | Status |
|----------|------------|-------------|-------------|-------------|--------|
| rrjson   | 47.264     | 26.098      | 26.182      | 2390        | ✓      |
| simdjson | 3.567      | 0.000       | 0.000       | 2390        | ✓      |

## Analysis

### Performance Comparison

**Parsing Speed:**
- simdjson is **6.3x faster** on small datasets
- simdjson is **23x faster** on medium datasets  
- simdjson is **13x faster** on large datasets

**Access Speed:**
- simdjson shows near-zero access time due to its lazy evaluation approach
- rrjson shows consistent access performance that scales with dataset size

**Error Handling:**
- simdjson shows near-zero error handling time due to its design
- rrjson has measurable but reasonable error handling overhead

**Memory Usage:**
- Both libraries show similar memory usage patterns
- Memory usage scales linearly with JSON size as expected

### Key Observations

1. **simdjson Dominance**: simdjson significantly outperforms rrjson in all parsing scenarios, which is expected given its SIMD optimizations and specialized design for parsing speed.

2. **rrjson Consistency**: rrjson shows consistent, predictable performance across all test scenarios with reasonable overhead.

3. **Scaling Behavior**: Both libraries scale reasonably with data size, though simdjson maintains its performance advantage.

4. **Access Patterns**: The difference in access time reflects different design philosophies:
   - simdjson uses lazy evaluation and on-demand parsing
   - rrjson provides immediate access to parsed data structures

### Trade-offs

**simdjson Advantages:**
- Extremely fast parsing
- Low memory overhead
- SIMD-optimized for modern CPUs
- Minimal access overhead due to lazy evaluation

**simdjson Limitations:**
- Read-only (no JSON generation)
- Requires modern CPU with SIMD support
- More complex API for some use cases

**rrjson Advantages:**
- Modern C++ design with concepts and trailing return types
- Comprehensive error handling with detailed exception types
- Resumable parsing capability
- Zero-copy string access using string_view
- Convenient API similar to popular libraries

**rrjson Areas for Improvement:**
- Parsing speed could be optimized
- Consider SIMD optimizations for numeric parsing
- Potential for lazy evaluation in some scenarios

## Recommendations

1. **For Maximum Speed**: Use simdjson when parsing speed is critical and you only need read access.

2. **For Balanced Performance**: rrjson provides good performance with modern C++ features and comprehensive error handling.

3. **For Production Use**: Consider the trade-offs between raw speed (simdjson) and API convenience/features (rrjson).

## Future Improvements for rrjson

Based on this benchmark, potential optimizations for rrjson could include:

1. **SIMD Optimizations**: Implement SIMD instructions for number parsing and string scanning
2. **Lazy Evaluation**: Consider lazy parsing for large objects/arrays
3. **Memory Pool**: Use custom memory allocators to reduce allocation overhead
4. **String Interning**: Cache frequently accessed strings
5. **Vectorized Operations**: Optimize bulk operations on arrays

## Running Additional Comparisons

To compare with more libraries, install them and rebuild:

```bash
# For Arch Linux
sudo pacman -S nlohmann-json rapidjson boost

# For Ubuntu/Debian  
sudo apt install nlohmann-json3-dev rapidjson-dev libboost-all-dev

# Then rebuild with additional libraries
cd benchmarks
./build_and_run.sh
```
