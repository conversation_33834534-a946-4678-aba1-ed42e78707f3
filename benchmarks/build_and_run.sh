#!/bin/bash

# JSON Library Benchmark Build and Run Script
# This script attempts to build and run the JSON benchmark with available libraries

set -e  # Exit on any error

echo "JSON Library Benchmark Build Script"
echo "==================================="
echo

# Check if we're in the right directory
if [ ! -f "json_benchmark.cpp" ]; then
    echo "Error: json_benchmark.cpp not found. Please run this script from the benchmarks directory."
    exit 1
fi

# Create build directory
BUILD_DIR="build"
if [ -d "$BUILD_DIR" ]; then
    echo "Removing existing build directory..."
    rm -rf "$BUILD_DIR"
fi

mkdir "$BUILD_DIR"
cd "$BUILD_DIR"

echo "Configuring build with CMake..."
echo

# Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

echo
echo "Building benchmark..."
echo

# Build
make -j$(nproc)

echo
echo "Build completed successfully!"
echo

# Check if executable was created
if [ ! -f "json_benchmark" ]; then
    echo "Error: json_benchmark executable not found after build."
    exit 1
fi

echo "Running benchmark..."
echo "==================="
echo

# Run the benchmark
./json_benchmark

echo
echo "Benchmark completed!"
echo
echo "Note: If some libraries show as 'not found', you can install them with:"
echo "  sudo apt install nlohmann-json3-dev rapidjson-dev libboost-all-dev"
echo "  # For simdjson, you may need to build from source"
