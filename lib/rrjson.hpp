#pragma once

#include <string_view>
#include <memory>
#include <vector>
#include <unordered_map>
#include <variant>
#include <stdexcept>
#include <string>
#include <limits>
#include <cctype>
#include <cmath>
#include <concepts>
#include <type_traits>

namespace rrjson {

/**
 * @brief JSON element types
 */
enum class ElementType {
    Null,
    Bool,
    Number,
    String,
    Array,
    Object
};

// Forward declarations
class Element;
class Parser;

/**
 * @brief Concepts for type conversion from JSON elements
 */
template<typename T>
concept JsonBoolConvertible = std::same_as<T, bool>;

template<typename T>
concept JsonNumericConvertible = (std::integral<T> || std::floating_point<T>) && !std::same_as<T, bool>;

template<typename T>
concept JsonStringConvertible = std::same_as<T, std::string_view> ||
                                std::same_as<T, std::string> ||
                                std::same_as<T, const char*>;

template<typename T>
concept JsonConvertible = JsonBoolConvertible<T> ||
                         JsonNumericConvertible<T> ||
                         JsonStringConvertible<T>;

/**
 * @brief Exception thrown when type conversion fails
 */
class type_error : public std::runtime_error {
private:
    ElementType actual_type_;
    std::string requested_type_;

    static auto build_error_message(ElementType actual, const std::string& requested) -> std::string {
        return "Element is not a " + requested + " type (actual type: " + element_type_to_string(actual) + ")";
    }

public:
    static auto element_type_to_string(ElementType type) -> std::string {
        switch (type) {
            case ElementType::Null:   return "null";
            case ElementType::Bool:   return "boolean";
            case ElementType::Number: return "number";
            case ElementType::String: return "string";
            case ElementType::Array:  return "array";
            case ElementType::Object: return "object";
            default: return "unknown";
        }
    }

    type_error(ElementType actual_type, const std::string& requested_type)
        : std::runtime_error(build_error_message(actual_type, requested_type))
        , actual_type_(actual_type)
        , requested_type_(requested_type) {}

    auto actual_type() const -> ElementType { return actual_type_; }
    auto requested_type() const -> const std::string& { return requested_type_; }
};

/**
 * @brief Exception thrown when index access fails
 */
class index_error : public std::runtime_error {
private:
    size_t index_;
    size_t size_;
    ElementType actual_type_;

public:
    index_error(size_t index, size_t size, ElementType actual_type)
        : std::runtime_error("Index " + std::to_string(index) + " is out of bounds (size: " +
                           std::to_string(size) + ", element type: " +
                           type_error::element_type_to_string(actual_type) + ")")
        , index_(index)
        , size_(size)
        , actual_type_(actual_type) {}

    auto index() const -> size_t { return index_; }
    auto size() const -> size_t { return size_; }
    auto actual_type() const -> ElementType { return actual_type_; }
};

/**
 * @brief Exception thrown when key access fails
 */
class key_error : public std::runtime_error {
private:
    std::string key_;
    ElementType actual_type_;

public:
    key_error(std::string_view key, ElementType actual_type)
        : std::runtime_error("Key '" + std::string(key) + "' not found in " +
                           type_error::element_type_to_string(actual_type) + " element")
        , key_(key)
        , actual_type_(actual_type) {}

    auto key() const -> const std::string& { return key_; }
    auto actual_type() const -> ElementType { return actual_type_; }
};

/**
 * @brief Exception thrown when JSON parsing fails
 */
class parse_error : public std::runtime_error {
private:
    size_t position_;
    char unexpected_char_;

public:
    parse_error(const std::string& message, size_t position, char unexpected_char = '\0')
        : std::runtime_error(message + " at position " + std::to_string(position) +
                           (unexpected_char != '\0' ? " (unexpected character: '" + std::string(1, unexpected_char) + "')" : ""))
        , position_(position)
        , unexpected_char_(unexpected_char) {}

    auto position() const -> size_t { return position_; }
    auto unexpected_char() const -> char { return unexpected_char_; }
};



/**
 * @brief Separate Parser class to handle all parsing logic and resume functionality
 */
class Parser : public std::enable_shared_from_this<Parser> {
public:
    using ArrayType = std::vector<Element>;
    using ObjectType = std::unordered_map<std::string_view, Element>;

private:
    std::string json_data_;                // Owned JSON data
    std::string_view json_view_;           // View of the JSON data
    mutable size_t current_pos_;

    // Resume-related data
    mutable Element* resume_element_ptr_;  // Element that can be resumed
    mutable size_t resume_pos_;            // Position where parsing can be resumed
    mutable bool can_resume_;              // Whether resume is possible

public:
    // Constructor with JSON data
    explicit Parser(std::string&& json_raw)
        : json_data_(std::move(json_raw))
        , json_view_(json_data_)
        , current_pos_(0)
        , resume_element_ptr_(nullptr)
        , resume_pos_(0)
        , can_resume_(false) {}

    // Delete copy operations
    Parser(const Parser&) = delete;
    auto operator=(const Parser&) -> Parser& = delete;

    // Allow move operations
    Parser(Parser&&) = default;
    auto operator=(Parser&&) -> Parser& = default;

    // Parse root element
    auto parse() -> Element;

    // Resume functionality
    auto can_resume() const -> bool { return can_resume_; }
    auto current_position() const -> size_t { return current_pos_; }
    auto data_size() const -> size_t { return json_data_.size(); }

    // Resume parsing for specific element
    auto try_resume_and_find_key(Element* element, std::string_view key) -> Element;
    auto try_resume_and_find_index(Element* element, size_t index) -> Element;

private:
    // Set resume context
    auto set_resume_context(Element* element, size_t pos) -> void {
        resume_element_ptr_ = element;
        resume_pos_ = pos;
        can_resume_ = true;
    }

    // Check if element can be resumed
    auto can_resume_element(Element* element) const -> bool {
        return can_resume_ && resume_element_ptr_ == element && resume_pos_ > 0;
    }

    // Parsing methods
    auto parse_value() -> Element;
    auto parse_string_element() -> Element;
    auto parse_number_element() -> Element;
    auto parse_literal() -> Element;
    auto parse_object() -> Element;
    auto parse_array() -> Element;

    // Resume parsing methods
    auto resume_object_parsing(Element* element) -> bool;
    auto resume_array_parsing(Element* element) -> bool;
    auto continue_object_parsing(ObjectType& obj) -> bool;
    auto continue_array_parsing(ArrayType& arr) -> bool;

    // Lexer methods
    auto skip_whitespace() const -> void {
        while (current_pos_ < json_view_.size() &&
               std::isspace(static_cast<unsigned char>(json_view_[current_pos_]))) {
            ++current_pos_;
        }
    }

    auto peek_char() const -> char {
        return current_pos_ < json_view_.size() ? json_view_[current_pos_] : '\0';
    }

    auto consume_char() const -> char {
        if (current_pos_ >= json_view_.size()) {
            throw parse_error("Unexpected end of input", current_pos_);
        }
        return json_view_[current_pos_++];
    }

    auto expect_char(char expected) const -> void {
        auto ch = consume_char();
        if (ch != expected) {
            throw parse_error("Expected '" + std::string(1, expected) + "'", current_pos_ - 1, ch);
        }
    }

    friend class Element;
};

/**
 * @brief Streamlined JSON element with minimal information
 */
class Element {
public:
    using ArrayType = std::vector<Element>;
    using ObjectType = std::unordered_map<std::string_view, Element>;
    using ValueType = std::variant<
        std::nullptr_t,     // Null
        bool,               // Bool
        double,             // Number
        std::string_view,   // String
        ArrayType,          // Array
        ObjectType          // Object
    >;

private:
    ValueType value_;
    std::shared_ptr<Parser> parser_;  // Reference to parser for resume functionality (only when needed)

    friend class Parser;

public:
    // Default constructor for null element
    Element() : value_(nullptr), parser_(nullptr) {}

    // Constructor for parsed elements with parser reference
    explicit Element(ValueType value, std::shared_ptr<Parser> parser = nullptr)
        : value_(std::move(value)), parser_(parser) {}

    // Constructor with JSON data - creates parser and parses
    explicit Element(std::string&& json_raw) {
        parser_ = std::make_shared<Parser>(std::move(json_raw));
        *this = parser_->parse();
    }

    // Copy constructor
    Element(const Element& other) = default;

    // Move constructor
    Element(Element&&) = default;

    // Assignment operators
    auto operator=(const Element& other) -> Element& = default;
    auto operator=(Element&&) -> Element& = default;

    // Type checking with trailing return type
    auto type() const -> ElementType {
        return static_cast<ElementType>(value_.index());
    }

    auto is_null() const -> bool { return type() == ElementType::Null; }
    auto is_bool() const -> bool { return type() == ElementType::Bool; }
    auto is_number() const -> bool { return type() == ElementType::Number; }
    auto is_string() const -> bool { return type() == ElementType::String; }
    auto is_array() const -> bool { return type() == ElementType::Array; }
    auto is_object() const -> bool { return type() == ElementType::Object; }

    // Concept-based conversion functions with trailing return type and error throwing
    template<typename T>
    auto get() const -> T requires JsonBoolConvertible<T> {
        if (auto* val = std::get_if<bool>(&value_)) {
            return *val;
        }
        throw type_error(type(), "boolean");
    }

    template<typename T>
    auto get() const -> T requires JsonNumericConvertible<T> {
        if (auto* val = std::get_if<double>(&value_)) {
            auto num = *val;

            // Check for infinity or NaN first
            if (!std::isfinite(num)) {
                throw std::overflow_error("Number value is not finite");
            }

            // For integral types, check for overflow and precision loss
            if constexpr (std::integral<T>) {
                if (num < std::numeric_limits<T>::lowest() || num > std::numeric_limits<T>::max()) {
                    throw std::overflow_error("Number value is out of range for requested type");
                }
                // Check for fractional part if converting to integral type
                if (num != std::trunc(num)) {
                    throw std::overflow_error("Number has fractional part, cannot convert to integral type");
                }
                return static_cast<T>(num);
            } else {
                return static_cast<T>(num);
            }
        }
        throw type_error(type(), std::integral<T> ? "integer" : "number");
    }

    template<typename T>
    auto get() const -> T requires JsonStringConvertible<T> {
        if (auto* val = std::get_if<std::string_view>(&value_)) {
            if constexpr (std::same_as<T, std::string_view>) {
                return *val;
            } else if constexpr (std::same_as<T, std::string>) {
                return std::string(*val);
            } else if constexpr (std::same_as<T, const char*>) {
                return val->data();
            }
        }
        throw type_error(type(), "string");
    }

    // Note: Implicit conversion operators removed to avoid ambiguity with operator[]
    // Use explicit get<T>() or as_xxx() methods instead

    // Convenience methods for common types (for backward compatibility and explicit usage)
    auto as_bool() const -> bool { return get<bool>(); }
    auto as_number() const -> double { return get<double>(); }  // For backward compatibility
    auto as_double() const -> double { return get<double>(); }
    auto as_float() const -> float { return get<float>(); }
    auto as_int() const -> int { return get<int>(); }
    auto as_long() const -> long { return get<long>(); }
    auto as_string() const -> std::string_view { return get<std::string_view>(); }
    auto as_std_string() const -> std::string { return get<std::string>(); }

    // Container accessors with trailing return type - support resumable parsing
    auto operator[](std::string_view key) const -> Element {
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            auto it = obj->find(key);
            if (it != obj->end()) {
                return it->second;
            }

            // Key not found - try to resume parsing if possible
            if (parser_) {
                return parser_->try_resume_and_find_key(const_cast<Element*>(this), key);
            }

            // Key not found and cannot resume
            throw key_error(key, type());
        }
        // Not an object type
        throw type_error(type(), "object");
    }

    auto operator[](size_t index) const -> Element {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            if (index < arr->size()) {
                return (*arr)[index];
            }

            // Index out of bounds - try to resume parsing if possible
            if (parser_) {
                return parser_->try_resume_and_find_index(const_cast<Element*>(this), index);
            }

            // Index out of bounds and cannot resume
            throw index_error(index, arr->size(), type());
        }
        // Not an array type
        throw type_error(type(), "array");
    }

    auto size() const -> size_t {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            return arr->size();
        }
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            return obj->size();
        }
        // Not a container type
        throw type_error(type(), "array or object");
    }

    // Explicit conversion to bool for validity checking
    explicit operator bool() const {
        return !is_null();
    }

    // Parser access methods
    auto has_parser() const -> bool { return parser_ != nullptr; }
    auto can_resume() const -> bool { return parser_ ? parser_->can_resume() : false; }
    auto current_position() const -> size_t { return parser_ ? parser_->current_position() : 0; }
    auto data_size() const -> size_t { return parser_ ? parser_->data_size() : 0; }

private:
    // Internal method to set parser reference for child elements
    auto set_parser(std::shared_ptr<Parser> parser) -> void { parser_ = parser; }
};

// Parser method implementations
inline auto Parser::parse() -> Element {
    current_pos_ = 0;
    can_resume_ = false;
    resume_element_ptr_ = nullptr;
    resume_pos_ = 0;

    skip_whitespace();
    if (current_pos_ >= json_view_.size()) {
        throw parse_error("Empty JSON input", current_pos_);
    }

    auto result = parse_value();
    skip_whitespace();

    // Check if there's remaining content for potential resume
    if (current_pos_ < json_view_.size()) {
        can_resume_ = true;
    }

    return result;
}

inline auto Parser::try_resume_and_find_key(Element* element, std::string_view key) -> Element {
    if (!can_resume_element(element)) {
        throw key_error(key, element->type());
    }

    // Try to resume parsing from the stored position
    auto* obj = std::get_if<ObjectType>(&element->value_);
    if (!obj) {
        throw key_error(key, element->type());
    }

    if (resume_object_parsing(element)) {
        // Check again after resuming
        auto it = obj->find(key);
        if (it != obj->end()) {
            return it->second;
        }
    }

    // Still not found after resuming
    throw key_error(key, element->type());
}

inline auto Parser::try_resume_and_find_index(Element* element, size_t index) -> Element {
    if (!can_resume_element(element)) {
        throw index_error(index, 0, element->type());
    }

    // Try to resume parsing from the stored position
    auto* arr = std::get_if<ArrayType>(&element->value_);
    if (!arr) {
        throw index_error(index, 0, element->type());
    }

    if (resume_array_parsing(element)) {
        // Check again after resuming
        if (index < arr->size()) {
            return (*arr)[index];
        }
    }

    // Still out of bounds after resuming
    throw index_error(index, arr->size(), element->type());
}

inline auto Parser::parse_value() -> Element {
    skip_whitespace();

    if (current_pos_ >= json_view_.size()) {
        throw parse_error("Unexpected end of input while parsing value", current_pos_);
    }

    switch (peek_char()) {
        case '"':  return parse_string_element();
        case '{':  return parse_object();
        case '[':  return parse_array();
        case 't':
        case 'f':
        case 'n':  return parse_literal();
        case '-':
        case '0': case '1': case '2': case '3': case '4':
        case '5': case '6': case '7': case '8': case '9':
            return parse_number_element();
        default:
            throw parse_error("Invalid JSON value", current_pos_, peek_char());
    }
}

inline auto Parser::parse_string_element() -> Element {
    expect_char('"');
    auto start = current_pos_;

    while (current_pos_ < json_view_.size() && json_view_[current_pos_] != '"') {
        if (json_view_[current_pos_] == '\\') {
            ++current_pos_; // Skip escape character
            if (current_pos_ >= json_view_.size()) {
                throw parse_error("Unterminated string escape", current_pos_);
            }
        }
        ++current_pos_;
    }

    if (current_pos_ >= json_view_.size()) {
        throw parse_error("Unterminated string", current_pos_);
    }

    auto string_view = json_view_.substr(start, current_pos_ - start);
    expect_char('"');

    return Element(string_view, shared_from_this());
}

inline auto Parser::parse_number_element() -> Element {
    auto start = current_pos_;

    // Handle negative sign
    if (peek_char() == '-') {
        ++current_pos_;
    }

    // Parse integer part
    if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
        throw parse_error("Invalid number", current_pos_, peek_char());
    }

    if (peek_char() == '0') {
        ++current_pos_;
    } else {
        while (current_pos_ < json_view_.size() &&
               std::isdigit(static_cast<unsigned char>(peek_char()))) {
            ++current_pos_;
        }
    }

    // Parse decimal part
    if (peek_char() == '.') {
        ++current_pos_;
        if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
            throw parse_error("Invalid number: missing digits after decimal", current_pos_);
        }
        while (current_pos_ < json_view_.size() &&
               std::isdigit(static_cast<unsigned char>(peek_char()))) {
            ++current_pos_;
        }
    }

    // Parse exponent part
    if (peek_char() == 'e' || peek_char() == 'E') {
        ++current_pos_;
        if (peek_char() == '+' || peek_char() == '-') {
            ++current_pos_;
        }
        if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
            throw parse_error("Invalid number: missing digits in exponent", current_pos_);
        }
        while (current_pos_ < json_view_.size() &&
               std::isdigit(static_cast<unsigned char>(peek_char()))) {
            ++current_pos_;
        }
    }

    auto number_str = json_view_.substr(start, current_pos_ - start);
    auto number = std::stod(std::string(number_str));

    // Check for infinity or NaN
    if (!std::isfinite(number)) {
        throw parse_error("Number value is not finite", start);
    }

    return Element(number, shared_from_this());
}

inline auto Parser::parse_literal() -> Element {
    if (current_pos_ + 4 <= json_view_.size() &&
        json_view_.substr(current_pos_, 4) == "true") {
        current_pos_ += 4;
        return Element(true, shared_from_this());
    }

    if (current_pos_ + 5 <= json_view_.size() &&
        json_view_.substr(current_pos_, 5) == "false") {
        current_pos_ += 5;
        return Element(false, shared_from_this());
    }

    if (current_pos_ + 4 <= json_view_.size() &&
        json_view_.substr(current_pos_, 4) == "null") {
        current_pos_ += 4;
        return Element(nullptr, shared_from_this());
    }

    throw parse_error("Invalid literal", current_pos_, peek_char());
}

inline auto Parser::parse_object() -> Element {
    expect_char('{');
    skip_whitespace();

    ObjectType obj;
    auto resume_pos = current_pos_;

    if (peek_char() == '}') {
        ++current_pos_;
        auto result = Element(std::move(obj), shared_from_this());
        set_resume_context(&result, resume_pos);
        return result;
    }

    while (true) {
        skip_whitespace();

        // Parse key
        if (peek_char() != '"') {
            throw parse_error("Expected string key in object", current_pos_, peek_char());
        }

        auto key_element = parse_string_element();
        auto key = key_element.get<std::string_view>();

        skip_whitespace();
        expect_char(':');
        skip_whitespace();

        // Parse value
        auto value = parse_value();
        obj[key] = std::move(value);

        skip_whitespace();

        if (peek_char() == '}') {
            ++current_pos_;
            break;
        }

        if (peek_char() == ',') {
            ++current_pos_;
            continue;
        }

        throw parse_error("Expected ',' or '}' in object", current_pos_, peek_char());
    }

    auto result = Element(std::move(obj), shared_from_this());
    set_resume_context(&result, resume_pos);
    return result;
}

inline auto Parser::parse_array() -> Element {
    expect_char('[');
    skip_whitespace();

    ArrayType arr;
    auto resume_pos = current_pos_;

    if (peek_char() == ']') {
        ++current_pos_;
        auto result = Element(std::move(arr), shared_from_this());
        set_resume_context(&result, resume_pos);
        return result;
    }

    while (true) {
        skip_whitespace();

        auto value = parse_value();
        arr.push_back(std::move(value));

        skip_whitespace();

        if (peek_char() == ']') {
            ++current_pos_;
            break;
        }

        if (peek_char() == ',') {
            ++current_pos_;
            continue;
        }

        throw parse_error("Expected ',' or ']' in array", current_pos_, peek_char());
    }

    auto result = Element(std::move(arr), shared_from_this());
    set_resume_context(&result, resume_pos);
    return result;
}

inline auto Parser::resume_object_parsing(Element* element) -> bool {
    if (!can_resume_element(element)) {
        return false;
    }

    auto saved_pos = current_pos_;
    current_pos_ = resume_pos_;

    try {
        auto* obj = std::get_if<ObjectType>(&element->value_);
        if (!obj) return false;

        // Continue parsing object from resume position
        return continue_object_parsing(*obj);
    } catch (...) {
        current_pos_ = saved_pos;
        return false;
    }
}

inline auto Parser::resume_array_parsing(Element* element) -> bool {
    if (!can_resume_element(element)) {
        return false;
    }

    auto saved_pos = current_pos_;
    current_pos_ = resume_pos_;

    try {
        auto* arr = std::get_if<ArrayType>(&element->value_);
        if (!arr) return false;

        // Continue parsing array from resume position
        return continue_array_parsing(*arr);
    } catch (...) {
        current_pos_ = saved_pos;
        return false;
    }
}

inline auto Parser::continue_object_parsing(ObjectType& obj) -> bool {
    try {
        skip_whitespace();

        while (current_pos_ < json_view_.size() && peek_char() != '}') {
            if (peek_char() == ',') {
                ++current_pos_;
                skip_whitespace();
            }

            if (peek_char() != '"') break;

            auto key_element = parse_string_element();
            auto key = key_element.get<std::string_view>();

            skip_whitespace();
            expect_char(':');
            skip_whitespace();

            auto value = parse_value();
            obj[key] = std::move(value);

            skip_whitespace();
        }

        return true;
    } catch (...) {
        return false;
    }
}

inline auto Parser::continue_array_parsing(ArrayType& arr) -> bool {
    try {
        skip_whitespace();

        while (current_pos_ < json_view_.size() && peek_char() != ']') {
            if (peek_char() == ',') {
                ++current_pos_;
                skip_whitespace();
            }

            auto value = parse_value();
            arr.push_back(std::move(value));

            skip_whitespace();
        }

        return true;
    } catch (...) {
        return false;
    }
}

} // namespace rrjson
