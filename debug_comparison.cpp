#include "lib/rrjson.hpp"
#include <iostream>
#include <limits>

int main() {
    try {
        std::string json = R"({"value": 1e19})";
        rrjson::Element root(std::move(json));
        
        auto number = root["value"].as_number();
        auto int_max_as_double = static_cast<double>(std::numeric_limits<int>::max());
        auto int_min_as_double = static_cast<double>(std::numeric_limits<int>::min());
        
        std::cout << "Number: " << number << std::endl;
        std::cout << "INT_MAX as double: " << int_max_as_double << std::endl;
        std::cout << "INT_MIN as double: " << int_min_as_double << std::endl;
        std::cout << "number > INT_MAX: " << (number > int_max_as_double) << std::endl;
        std::cout << "number < INT_MIN: " << (number < int_min_as_double) << std::endl;
        
        // Let's see what the condition actually evaluates to
        bool overflow_condition = (number > int_max_as_double) || (number < int_min_as_double);
        std::cout << "Overflow condition: " << overflow_condition << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
