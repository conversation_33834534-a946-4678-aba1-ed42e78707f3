[{"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-Wall", "-Werror", "-O3", "-std=c++23", "-Ibuild/.gens/comprehensive/linux/x86_64/release/platform/windows/idl", "-Il<PERSON>", "-DNDEBUG", "-o", "build/.objs/comprehensive/linux/x86_64/release/examples/comprehensive_test.cpp.o", "examples/comprehensive_test.cpp"], "file": "examples/comprehensive_test.cpp"}, {"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-Wall", "-Werror", "-O3", "-std=c++23", "-Ibuild/.gens/benchmark/linux/x86_64/release/platform/windows/idl", "-Il<PERSON>", "-DHAS_SIMDJSON", "-DNDEBUG", "-o", "build/.objs/benchmark/linux/x86_64/release/benchmarks/json_benchmark.cpp.o", "benchmarks/json_benchmark.cpp"], "file": "benchmarks/json_benchmark.cpp"}, {"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-Wall", "-Werror", "-O3", "-std=c++23", "-Ibuild/.gens/performance/linux/x86_64/release/platform/windows/idl", "-Il<PERSON>", "-DNDEBUG", "-o", "build/.objs/performance/linux/x86_64/release/examples/performance_test.cpp.o", "examples/performance_test.cpp"], "file": "examples/performance_test.cpp"}, {"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-Wall", "-Werror", "-O3", "-std=c++23", "-Ibuild/.gens/test/linux/x86_64/release/platform/windows/idl", "-Il<PERSON>", "-DNDEBUG", "-o", "build/.objs/test/linux/x86_64/release/tests/test_parser.cpp.o", "tests/test_parser.cpp"], "file": "tests/test_parser.cpp"}, {"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-fvisibility=hidden", "-fvisibility-inlines-hidden", "-Wall", "-Werror", "-O3", "-std=c++23", "-Ibuild/.gens/example/linux/x86_64/release/platform/windows/idl", "-Il<PERSON>", "-DNDEBUG", "-o", "build/.objs/example/linux/x86_64/release/examples/basic_usage.cpp.o", "examples/basic_usage.cpp"], "file": "examples/basic_usage.cpp"}]