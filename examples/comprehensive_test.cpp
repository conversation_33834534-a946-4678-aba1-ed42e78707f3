#include "rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    std::cout << "rrjson Comprehensive Test\n";
    std::cout << "=========================\n\n";
    
    // Complex JSON with all types
    std::string complex_json = R"({
        "string": "Hello, World!",
        "number": 42.5,
        "integer": 123,
        "boolean_true": true,
        "boolean_false": false,
        "null_value": null,
        "array": [1, "two", 3.14, true, null],
        "nested_object": {
            "level1": {
                "level2": {
                    "deep_value": "Found me!"
                }
            }
        },
        "mixed_array": [
            {"name": "Alice", "age": 25},
            {"name": "<PERSON>", "age": 30},
            {"name": "<PERSON>", "age": 35}
        ]
    })";
    
    try {
        std::cout << "Parsing complex JSON...\n";
        rrjson::Element root(std::move(complex_json));
        
        std::cout << "✓ Successfully parsed " << root.data_size() << " bytes\n\n";
        
        // Test all basic types
        std::cout << "Testing basic types:\n";
        std::cout << "String: '" << root["string"].as_string() << "'\n";
        std::cout << "Number: " << root["number"].as_number() << "\n";
        std::cout << "Integer: " << root["integer"].as_int() << "\n";
        std::cout << "Boolean true: " << (root["boolean_true"].as_bool() ? "true" : "false") << "\n";
        std::cout << "Boolean false: " << (root["boolean_false"].as_bool() ? "true" : "false") << "\n";
        std::cout << "Null is null: " << (root["null_value"].is_null() ? "yes" : "no") << "\n\n";
        
        // Test array access
        std::cout << "Testing array access:\n";
        auto array = root["array"];
        std::cout << "Array size: " << array.size() << "\n";
        for (size_t i = 0; i < array.size(); ++i) {
            auto elem = array[i];
            std::cout << "  [" << i << "]: ";
            if (elem.is_number()) {
                std::cout << elem.as_number() << " (number)";
            } else if (elem.is_string()) {
                std::cout << "'" << elem.as_string() << "' (string)";
            } else if (elem.is_bool()) {
                std::cout << (elem.as_bool() ? "true" : "false") << " (boolean)";
            } else if (elem.is_null()) {
                std::cout << "null";
            }
            std::cout << "\n";
        }
        std::cout << "\n";
        
        // Test deep nesting
        std::cout << "Testing deep nesting:\n";
        auto deep_value = root["nested_object"]["level1"]["level2"]["deep_value"];
        std::cout << "Deep value: '" << deep_value.as_string() << "'\n\n";
        
        // Test mixed array with objects
        std::cout << "Testing mixed array with objects:\n";
        auto mixed_array = root["mixed_array"];
        for (size_t i = 0; i < mixed_array.size(); ++i) {
            auto person = mixed_array[i];
            std::cout << "  Person " << i << ": " 
                      << person["name"].as_string() << " (age: " 
                      << person["age"].as_int() << ")\n";
        }
        std::cout << "\n";
        
        // Test error handling
        std::cout << "Testing error handling:\n";
        
        try {
            auto nonexistent = root["nonexistent_key"];
            std::cout << "✗ Should have thrown key_error\n";
        } catch (const rrjson::key_error& e) {
            std::cout << "✓ Key error: " << e.what() << "\n";
        }
        
        try {
            auto out_of_bounds = array[100];
            std::cout << "✗ Should have thrown index_error\n";
        } catch (const rrjson::index_error& e) {
            std::cout << "✓ Index error: " << e.what() << "\n";
        }
        
        try {
            auto wrong_type = root["string"].as_number();
            (void)wrong_type; // Suppress unused variable warning
            std::cout << "✗ Should have thrown type_error\n";
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Type error: " << e.what() << "\n";
        }
        
        std::cout << "\n✓ All tests passed successfully!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "✗ Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
