#include "rrjson.hpp"
#include <iostream>
#include <string>
#include <chrono>
#include <sstream>

auto generate_large_json(size_t num_objects) -> std::string {
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"metadata\": {\n";
    oss << "    \"version\": \"1.0\",\n";
    oss << "    \"count\": " << num_objects << ",\n";
    oss << "    \"generated\": true\n";
    oss << "  },\n";
    oss << "  \"data\": [\n";
    
    for (size_t i = 0; i < num_objects; ++i) {
        if (i > 0) oss << ",\n";
        oss << "    {\n";
        oss << "      \"id\": " << i << ",\n";
        oss << "      \"name\": \"Object " << i << "\",\n";
        oss << "      \"value\": " << (i * 3.14) << ",\n";
        oss << "      \"active\": " << (i % 2 == 0 ? "true" : "false") << ",\n";
        oss << "      \"tags\": [\"tag" << i << "\", \"category" << (i % 5) << "\"]\n";
        oss << "    }";
    }
    
    oss << "\n  ]\n";
    oss << "}";
    
    return oss.str();
}

int main() {
    std::cout << "rrjson Performance Test\n";
    std::cout << "=======================\n\n";
    
    const size_t num_objects = 1000;
    
    std::cout << "Generating JSON with " << num_objects << " objects...\n";
    auto json_data = generate_large_json(num_objects);
    std::cout << "Generated " << json_data.size() << " bytes of JSON data\n\n";
    
    // Test parsing performance
    std::cout << "Testing parsing performance...\n";
    auto start = std::chrono::high_resolution_clock::now();
    
    try {
        rrjson::Element root(std::move(json_data));
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "✓ Parsing completed in " << duration.count() << " microseconds\n";
        std::cout << "✓ Data size: " << root.data_size() << " bytes\n";
        std::cout << "✓ Root type: " << (root.is_object() ? "Object" : "Other") << "\n\n";
        
        // Test access performance
        std::cout << "Testing access performance...\n";
        start = std::chrono::high_resolution_clock::now();
        
        auto metadata = root["metadata"];
        auto version = metadata["version"].as_string();
        auto count = metadata["count"].as_int();
        auto data_array = root["data"];
        auto array_size = data_array.size();
        
        // Access some elements
        size_t total_accessed = 0;
        for (size_t i = 0; i < std::min(array_size, size_t(100)); ++i) {
            auto obj = data_array[i];
            auto id = obj["id"].as_int();
            auto name = obj["name"].as_string();
            auto value = obj["value"].as_number();
            auto active = obj["active"].as_bool();
            
            total_accessed += id;
            (void)name; (void)value; (void)active; // Suppress warnings
        }
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "✓ Access completed in " << duration.count() << " microseconds\n";
        std::cout << "✓ Version: '" << version << "'\n";
        std::cout << "✓ Count: " << count << "\n";
        std::cout << "✓ Array size: " << array_size << "\n";
        std::cout << "✓ Total accessed: " << total_accessed << "\n\n";
        
        // Test error handling performance
        std::cout << "Testing error handling...\n";
        start = std::chrono::high_resolution_clock::now();
        
        size_t errors_caught = 0;
        
        // Test key errors
        for (int i = 0; i < 100; ++i) {
            try {
                auto nonexistent = root["nonexistent_key_" + std::to_string(i)];
                (void)nonexistent;
            } catch (const rrjson::key_error&) {
                ++errors_caught;
            }
        }
        
        // Test index errors
        for (int i = 0; i < 100; ++i) {
            try {
                auto out_of_bounds = data_array[array_size + i];
                (void)out_of_bounds;
            } catch (const rrjson::index_error&) {
                ++errors_caught;
            }
        }
        
        // Test type errors
        for (int i = 0; i < 100; ++i) {
            try {
                auto wrong_type = metadata["version"].as_number(); // String as number
                (void)wrong_type;
            } catch (const rrjson::type_error&) {
                ++errors_caught;
            }
        }
        
        end = std::chrono::high_resolution_clock::now();
        duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        std::cout << "✓ Error handling completed in " << duration.count() << " microseconds\n";
        std::cout << "✓ Errors caught: " << errors_caught << "/300\n\n";
        
        std::cout << "🎉 Performance test completed successfully!\n";
        std::cout << "📊 Summary:\n";
        std::cout << "   - Parsed " << num_objects << " objects efficiently\n";
        std::cout << "   - Zero-copy string access using string_view\n";
        std::cout << "   - Robust error handling with detailed exceptions\n";
        std::cout << "   - Modern C++ features throughout\n";
        
    } catch (const std::exception& e) {
        std::cerr << "✗ Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
