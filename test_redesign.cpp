#include "lib/rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    try {
        // Test basic parsing
        std::string json_data = R"({
            "name": "<PERSON>",
            "age": 30,
            "active": true,
            "scores": [85, 92, 78],
            "address": {
                "street": "123 Main St",
                "city": "New York"
            }
        })";

        // Create element with streamlined design
        rrjson::Element root(std::move(json_data));

        // Test basic access
        std::cout << "Name: " << root["name"].as_string() << std::endl;
        std::cout << "Age: " << root["age"].as_int() << std::endl;
        std::cout << "Active: " << (root["active"].as_bool() ? "true" : "false") << std::endl;

        // Test array access
        auto scores = root["scores"];
        std::cout << "Scores: ";
        for (size_t i = 0; i < scores.size(); ++i) {
            std::cout << scores[i].as_int();
            if (i < scores.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;

        // Test nested object access
        auto address = root["address"];
        std::cout << "Address: " << address["street"].as_string() 
                  << ", " << address["city"].as_string() << std::endl;

        // Test parser information
        std::cout << "Has parser: " << (root.has_parser() ? "yes" : "no") << std::endl;
        std::cout << "Can resume: " << (root.can_resume() ? "yes" : "no") << std::endl;
        std::cout << "Data size: " << root.data_size() << std::endl;

        std::cout << "Test passed!" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
