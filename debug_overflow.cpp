#include "lib/rrjson.hpp"
#include <iostream>
#include <limits>
#include <cmath>

int main() {
    try {
        // Test what happens with a very large number
        std::string json = R"({"value": 9999999999999999999})";
        rrjson::Element root(std::move(json));
        
        auto number = root["value"].as_number();
        std::cout << "Parsed number: " << number << std::endl;
        std::cout << "Is finite: " << std::isfinite(number) << std::endl;
        std::cout << "INT_MAX: " << std::numeric_limits<int>::max() << std::endl;
        std::cout << "INT_MIN: " << std::numeric_limits<int>::min() << std::endl;
        std::cout << "Number > INT_MAX: " << (number > static_cast<double>(std::numeric_limits<int>::max())) << std::endl;
        
        try {
            auto int_val = root["value"].as_int();
            std::cout << "Converted to int: " << int_val << std::endl;
        } catch (const std::overflow_error& e) {
            std::cout << "Caught overflow_error: " << e.what() << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
