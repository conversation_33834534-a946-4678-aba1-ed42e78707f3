#include "lib/rrjson.hpp"
#include <iostream>
#include <limits>

int main() {
    try {
        std::string json = R"({"value": 1e19})";
        rrjson::Element root(std::move(json));
        
        std::cout << "About to call as_int()..." << std::endl;
        
        try {
            auto result = root["value"].as_int();
            std::cout << "as_int() returned: " << result << std::endl;
            std::cout << "ERROR: Should have thrown exception!" << std::endl;
        } catch (const std::overflow_error& e) {
            std::cout << "SUCCESS: Caught overflow_error: " << e.what() << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Caught other exception: " << e.what() << std::endl;
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
