#include "lib/rrjson.hpp"
#include <iostream>

int main() {
    try {
        std::string json_data = R"({"test": 42})";
        rrjson::Element root(std::move(json_data));
        
        // Test all functionality to catch any warnings
        auto value = root["test"];
        std::cout << "Value: " << value.as_int() << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
